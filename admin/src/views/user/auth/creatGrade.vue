<template>
  <el-dialog
    v-if="dialogVisible"
    :title="title"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose" >
    <el-form :model="formValidate" :rules="currentRules" ref="user" label-width="100px" class="demo-ruleForm" v-loading="loading">
      <!-- 新增：新增模式表单 -->
      <template v-if="type=='new'">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="formValidate.userId" placeholder="请输入用户ID"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formValidate.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="证件类型" prop="type">
          <el-select v-model="formValidate.type" placeholder="选择" class="selWidth">
            <el-option :value="item.type" v-for="(item, index) in typeList" :key="index" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码" prop="cerNo">
          <el-input v-model="formValidate.cerNo" placeholder="请输入证件号码"></el-input>
        </el-form-item>
        <el-form-item label="证件照片" prop="cerPic">
          <el-upload
            action
            :http-request="handleUploadForm"
            :headers="uploadHeaders"
            :on-remove="handleRemove"
            :file-list="fileList"
            list-type="picture-card"
            :limit="1"
            class="selWidth">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="formValidate.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
      </template>
      <template v-if="type=='add'">
        <el-form-item label="状态">
          <el-select v-model="status" placeholder="选择" class="selWidth">
            <el-option :value="item.type" v-for="(item, index) in statusList" :key="index" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <template v-else-if="type=='edit'">
        <el-form-item label="用户ID">
          <el-input v-model="formValidate.userId" placeholder="请输入用户ID"></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="formValidate.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="证件号码">
          <el-input v-model="formValidate.cerNo" placeholder="请输入证件号码"></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="formValidate.type" placeholder="选择" class="selWidth">
            <el-option :value="item.type" v-for="(item, index) in typeList" :key="index" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="状态">
          <el-select v-model="formValidate.status" placeholder="选择" class="selWidth">
            <el-option :value="item.type" v-for="(item, index) in statusList" :key="index" :label="item.name"></el-option>
          </el-select>
        </el-form-item>-->

        <el-form-item label="备注">
          <el-input v-model="formValidate.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
        <el-form-item label="处理结果">
          <el-input v-model="formValidate.result" placeholder="请输入处理结果"></el-input>
        </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="resetForm('user')">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm('user')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { authreview, authupdate, authsubmit } from '@/api/user'
import { fileImageApi } from '@/api/systemSetting' // 新增：引入 fileImageApi
import { Debounce } from '@/utils/validate'
import { getToken } from '@/utils/auth' // 新增：引入 getToken
export default {
  name: 'CreatGrade',
  props: ['statusList', 'typeList'],
  data() {
    return {
      dialogVisible: false,
      formValidate: {},
      loading: false,
      title: '',
      type: '',
      status: '',
      fileList: [],
      currentRules: {},
      uploadHeaders: {
        'X-Token': getToken()
      },
      rules: {
        userId: [
          { required: true, message: '请输入用户ID', trigger: 'blur' },
          { pattern: /^\d+$/, message: '用户ID必须为数字', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择证件类型', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        cerNo: [
          { required: true, message: '请输入证件号码', trigger: 'blur' }
        ],
        cerPic: [
          { required: true, message: '请上传证件照片', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    info(type, row) {
      this.dialogVisible = true
      this.type = type
      if (type == 'new') {
        this.title = '新增实名认证'
        this.formValidate = {
          userId: '',
          type: '',
          name: '',
          cerNo: '',
          cerPic: '',
          remark: ''
        }
        this.fileList = []
        this.currentRules = this.rules
      } else {
        this.formValidate = { ...row } // 深拷贝以避免修改原数据
        if (type == 'add') {
          this.title = '实名认证审核'
          this.currentRules = {}
        } else {
          this.title = '编辑实名认证'
          this.currentRules = {}
        }
        if (row.cerPic) {
          this.fileList = [{ name: 'cerPic', url: row.cerPic }]
        } else {
          this.fileList = []
        }
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 新增：自定义上传方法，模仿范例
    handleUploadForm(param) {
      const formData = new FormData()
      formData.append('multipart', param.file)
      const data = {
        model: 'maintain',
        pid: 0
      }
      const loading = this.$loading({
        lock: true,
        text: '上传中，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      fileImageApi(formData, data)
        .then(res => {
          loading.close()
          if (res) {
            this.formValidate.cerPic = res.url
            this.$refs.user.validateField('cerPic')
            this.$message.success('上传成功')
          } else {
            this.$message.error(res.message || '上传图片失败')
          }
        })
        .catch((res) => {
          console.log(res)
          loading.close()
          this.$message.error('上传图片失败')
        })
    },
    handleRemove() {
      this.formValidate.cerPic = ''
      this.$refs.user.validateField('cerPic')
    },
    submitForm: Debounce(function(formName) {
      this.$refs.user.validate((valid) => {
        if (valid || this.type != 'new') {
          this.loading = true
          if (this.type == 'new') {
            const data = {
              userId: parseInt(this.formValidate.userId),
              type: this.formValidate.type,
              name: this.formValidate.name,
              cerNo: this.formValidate.cerNo,
              cerPic: this.formValidate.cerPic,
              metaInfo: '{}'
            }
            authsubmit(data).then(res => {
              this.$message.success('提交成功')
              this.loading = false
              this.handleClose()
              this.$emit('getList')
            }).catch(() => {
              this.loading = false
            })
          } else if (this.type == 'add') {
            const data = {
              id: this.formValidate.id,
              status: this.status
            }
            authreview(data).then(res => {
              this.$message.success('审核成功')
              this.loading = false
              this.handleClose()
              this.$emit('getList')
            }).catch(() => {
              this.loading = false
            })
          } else {
            authupdate(this.formValidate).then(res => {
              this.$message.success('编辑成功')
              this.loading = false
              this.handleClose()
              this.$emit('getList')
            }).catch(() => {
              this.loading = false
            })
          }
        } else {
          return false
        }
      })
    }),
    resetForm(formName) {
      this.$nextTick(() => {
        this.$refs.user.resetFields()
      })
      this.dialogVisible = false
      this.fileList = []
    }
  }
}
</script>

<style scoped>
.selWidth {
  width: 100%;
}
</style>
